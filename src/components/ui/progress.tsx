"use client"

import * as React from "react"
import * as ProgressPrimitive from "@radix-ui/react-progress"

import { cn } from "@/lib/utils"

interface ProgressProps extends React.ComponentProps<typeof ProgressPrimitive.Root> {
  showValue?: boolean;
}

function Progress({
  className,
  value,
  showValue = false,
  ...props
}: ProgressProps) {
  return (
    <div className="relative">
      <ProgressPrimitive.Root
        data-slot="progress"
        className={cn(
          "relative h-2 w-full overflow-hidden rounded-full",
          "bg-[var(--progress-bg)] border border-[var(--progress-border)]",
          className
        )}
        {...props}
      >
        <ProgressPrimitive.Indicator
          data-slot="progress-indicator"
          className="h-full w-full flex-1 transition-all bg-[var(--progress-fill)]"
          style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
        />
      </ProgressPrimitive.Root>
      {showValue && (
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-xs font-medium text-[var(--progress-text)] drop-shadow-sm">
            {Math.round(value || 0)}%
          </span>
        </div>
      )}
    </div>
  )
}

export { Progress }
