# DottedBackground Component

A reusable React component that creates a dotted background pattern with fade edges, multiple colors, and variable dot sizes.

## Features

- **Variable dot sizes**: Dots randomly use different sizes from provided array
- **Multiple colors**: Different color palettes for light and dark modes
- **Adjustable spacing**: Control space between dots
- **Fade edges**: Configurable fade percentage for smooth edge transitions
- **Static design**: No animations - dots are randomly assigned properties once and stay consistent
- **Dark mode support**: Separate opacity settings and color arrays for light and dark themes
- **Flexible grid**: Adjustable number of dots per row/column

## Usage

```tsx
import { DottedBackground } from "@/components/ui/dotted-background";

// Basic usage
<DottedBackground />

// Customized usage
<DottedBackground
  fadeEdge={85}
  dotSizes={[1.5, 2, 2.5]}
  spacing={20}
  dotsPerRow={8}
  opacity={0.3}
  darkOpacity={0.4}
  lightColors={["888888", "999999", "777777"]}
  darkColors={["AAAAAA", "BBBBBB", "999999"]}
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `fadeEdge` | `number` | `80` | Fade edge percentage (0-100) |
| `dotSizes` | `number[]` | `[2]` | Array of dot sizes in pixels - dots randomly use one |
| `spacing` | `number` | `20` | Space between dots in pixels |
| `dotsPerRow` | `number` | `8` | Number of dots per row/column |
| `opacity` | `number` | `0.3` | Base opacity for light mode |
| `darkOpacity` | `number` | `0.4` | Base opacity for dark mode |
| `lightColors` | `string[]` | `["888888"]` | Array of colors for light mode (hex without #) |
| `darkColors` | `string[]` | `["AAAAAA"]` | Array of colors for dark mode (hex without #) |
| `className` | `string` | `""` | Additional CSS classes |

## Examples

### Dashboard Background

```tsx
<DottedBackground
  fadeEdge={85}
  dotSizes={[1.5, 2, 2.5]}
  spacing={20}
  dotsPerRow={8}
  opacity={0.3}
  darkOpacity={0.4}
  lightColors={["888888", "999999", "777777"]}
  darkColors={["AAAAAA", "BBBBBB", "999999"]}
/>
```

### Landing Page Background

```tsx
<DottedBackground
  fadeEdge={90}
  dotSizes={[1, 1.5, 2]}
  spacing={25}
  dotsPerRow={6}
  opacity={0.2}
  darkOpacity={0.3}
  lightColors={["CCCCCC", "BBBBBB", "DDDDDD"]}
  darkColors={["666666", "777777", "555555"]}
/>
```

### Subtle Background

```tsx
<DottedBackground
  fadeEdge={95}
  dotSizes={[0.5, 1, 1.5]}
  spacing={30}
  dotsPerRow={10}
  opacity={0.1}
  darkOpacity={0.15}
  lightColors={["EEEEEE", "DDDDDD", "CCCCCC"]}
  darkColors={["444444", "555555", "333333"]}
/>
```

## Implementation Notes

- The component generates a uniform grid of static dots with consistent spacing
- Fade edges are created using multiple gradient overlays for smooth transitions
- The pattern tiles seamlessly across the entire background
- All positioning is absolute, so ensure parent has `relative` positioning
- The component is optimized for performance with SVG-based patterns
- No animations or random variations for predictable, clean patterns

## Current Usage

- **Dashboard**: `/user-dashboard` - Medium visibility dots with 20px spacing
- **Landing Page**: `/` - Subtle dots with 25px spacing and high fade
- **Home Page**: Same as landing page

## Customization Tips

- **Subtle effect**: Use high `fadeEdge` (90+), low `opacity` (0.1-0.2)
- **Prominent effect**: Use lower `fadeEdge` (70-80), higher `opacity` (0.3-0.5)
- **Dense pattern**: Decrease `spacing`, increase `dotsPerRow`
- **Sparse pattern**: Increase `spacing`, decrease `dotsPerRow`
- **Larger dots**: Increase `dotSize` for more prominent dots
- **Smaller dots**: Decrease `dotSize` for more subtle effect
