/**
 * Design Token Constants
 * 
 * This file provides TypeScript constants for design tokens,
 * making them easier to use in components and ensuring type safety.
 */

// Brand Colors (from logo)
export const BRAND_COLORS = {
  LIGHT: '#b4fd98',    // Light green ring
  MEDIUM: '#73ed47',   // Border green
  DARK: '#0A4000',     // Dark green crescent
  WHITE: '#fff',       // White circle
} as const;

// CSS Variable Names
export const CSS_VARS = {
  // Brand Colors
  BRAND_LIGHT: 'var(--brand-light)',
  BRAND_MEDIUM: 'var(--brand-medium)',
  BRAND_DARK: 'var(--brand-dark)',
  BRAND_WHITE: 'var(--brand-white)',
  
  // Primary Colors
  PRIMARY: 'var(--primary)',
  PRIMARY_LIGHT: 'var(--primary-light)',
  PRIMARY_DARK: 'var(--primary-dark)',
  PRIMARY_FOREGROUND: 'var(--primary-foreground)',
  
  // Progress Bar Colors
  PROGRESS_BG: 'var(--progress-bg)',
  PROGRESS_FILL: 'var(--progress-fill)',
  PROGRESS_BORDER: 'var(--progress-border)',
  PROGRESS_TEXT: 'var(--progress-text)',
  
  // Semantic Colors
  DESTRUCTIVE: 'var(--destructive)',
  DESTRUCTIVE_FOREGROUND: 'var(--destructive-foreground)',
  ACCENT: 'var(--accent)',
  ACCENT_FOREGROUND: 'var(--accent-foreground)',
  
  // Surface Colors
  BACKGROUND: 'var(--background)',
  FOREGROUND: 'var(--foreground)',
  CARD: 'var(--card)',
  CARD_FOREGROUND: 'var(--card-foreground)',
  POPOVER: 'var(--popover)',
  POPOVER_FOREGROUND: 'var(--popover-foreground)',
  
  // Border & Input Colors
  BORDER: 'var(--border)',
  INPUT: 'var(--input)',
  RING: 'var(--ring)',
  
  // Neutral Colors
  MUTED: 'var(--muted)',
  MUTED_FOREGROUND: 'var(--muted-foreground)',
  
  // Chart Colors
  CHART_1: 'var(--chart-1)',
  CHART_2: 'var(--chart-2)',
  CHART_3: 'var(--chart-3)',
  CHART_4: 'var(--chart-4)',
  CHART_5: 'var(--chart-5)',
} as const;

// Tailwind CSS Classes for Common Patterns
export const TAILWIND_CLASSES = {
  // Progress Bar
  PROGRESS_BAR: 'w-full bg-[var(--progress-bg)] border border-[var(--progress-border)] rounded-full',
  PROGRESS_FILL: 'bg-[var(--progress-fill)] h-full rounded-full transition-all',
  PROGRESS_TEXT: 'text-[var(--progress-text)]',
  
  // Buttons
  BUTTON_PRIMARY: 'bg-[var(--primary)] hover:bg-[var(--primary-dark)] text-[var(--primary-foreground)]',
  BUTTON_SECONDARY: 'bg-[var(--secondary)] hover:bg-[var(--secondary)]/90 text-[var(--secondary-foreground)]',
  BUTTON_DESTRUCTIVE: 'bg-[var(--destructive)] hover:bg-[var(--destructive)]/90 text-[var(--destructive-foreground)]',
  
  // Cards
  CARD: 'bg-[var(--card)] text-[var(--card-foreground)] border border-[var(--border)]',
  
  // Focus States
  FOCUS_RING: 'focus:ring-2 focus:ring-[var(--ring)] focus:ring-offset-2',
} as const;

// Spacing Scale (in rem)
export const SPACING = {
  XS: '0.25rem',   // 4px
  SM: '0.5rem',    // 8px
  MD: '1rem',      // 16px
  LG: '1.5rem',    // 24px
  XL: '2rem',      // 32px
  '2XL': '3rem',   // 48px
  '3XL': '4rem',   // 64px
} as const;

// Border Radius
export const BORDER_RADIUS = {
  NONE: '0',
  SM: '0.125rem',  // 2px
  MD: '0.375rem',  // 6px
  LG: '0.5rem',    // 8px
  XL: '0.75rem',   // 12px
  '2XL': '1rem',   // 16px
  FULL: '9999px',
} as const;

// Typography
export const TYPOGRAPHY = {
  FONT_SANS: 'var(--font-sans)',
  FONT_SERIF: 'var(--font-serif)',
  FONT_MONO: 'var(--font-mono)',
} as const;

// Shadows
export const SHADOWS = {
  XS: 'var(--shadow-xs)',
  SM: 'var(--shadow-sm)',
  MD: 'var(--shadow-md)',
  LG: 'var(--shadow-lg)',
  XL: 'var(--shadow-xl)',
  '2XL': 'var(--shadow-2xl)',
} as const;

// Utility Functions
export const designTokens = {
  // Get CSS variable value
  cssVar: (variable: keyof typeof CSS_VARS) => CSS_VARS[variable],
  
  // Get brand color
  brandColor: (color: keyof typeof BRAND_COLORS) => BRAND_COLORS[color],
  
  // Get Tailwind class
  tailwindClass: (className: keyof typeof TAILWIND_CLASSES) => TAILWIND_CLASSES[className],
  
  // Get spacing value
  spacing: (size: keyof typeof SPACING) => SPACING[size],
  
  // Get border radius value
  borderRadius: (radius: keyof typeof BORDER_RADIUS) => BORDER_RADIUS[radius],
} as const;

// Type definitions for better TypeScript support
export type BrandColor = keyof typeof BRAND_COLORS;
export type CSSVariable = keyof typeof CSS_VARS;
export type TailwindClass = keyof typeof TAILWIND_CLASSES;
export type SpacingSize = keyof typeof SPACING;
export type BorderRadiusSize = keyof typeof BORDER_RADIUS;

// Example usage:
// import { designTokens, CSS_VARS } from '@/lib/design-tokens';
// 
// // In a component:
// <div className={designTokens.tailwindClass('PROGRESS_BAR')}>
//   <div className={designTokens.tailwindClass('PROGRESS_FILL')} />
// </div>
// 
// // Or with CSS variables:
// <div style={{ backgroundColor: CSS_VARS.PRIMARY }}>
//   Content
// </div> 